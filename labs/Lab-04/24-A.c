#include <stdio.h>
#include <stdlib.h>

int main() {
    int *arr, n, pos, val, i;

    printf("Enter size of array: ");
    scanf("%d", &n);

    // Validate input
    if (n <= 0) {
        printf("Invalid array size!\n");
        return 1;
    }

    // Dynamically allocate memory for n+1 elements (to accommodate insertion)
    arr = (int*)malloc((n + 1) * sizeof(int));
    if (arr == NULL) {
        printf("Memory allocation failed!\n");
        return 1;
    }

    printf("Enter %d elements:\n", n);
    for (i = 0; i < n; i++)
        scanf("%d", &arr[i]);

    printf("Enter position to insert: ");
    scanf("%d", &pos);

    // Validate position
    if (pos < 1 || pos > n + 1) {
        printf("Invalid position! Position should be between 1 and %d\n", n + 1);
        free(arr);
        return 1;
    }

    printf("Enter value to insert: ");
    scanf("%d", &val);

    // Shift elements to make space for insertion
    for (i = n; i >= pos; i--)
        arr[i] = arr[i - 1];
    arr[pos - 1] = val;
    n++;

    printf("Array after insertion:\n");
    for (i = 0; i < n; i++)
        printf("%d ", arr[i]);
    printf("\n");

    // Free allocated memory
    free(arr);
    return 0;
}
