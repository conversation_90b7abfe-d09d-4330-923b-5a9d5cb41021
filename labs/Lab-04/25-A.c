#include <stdio.h>
#include <stdlib.h>

int main() {
    int *arr, n, pos, i;

    printf("Enter size of array: ");
    scanf("%d", &n);

    // Validate input
    if (n <= 0) {
        printf("Invalid array size!\n");
        return 1;
    }

    // Dynamically allocate memory
    arr = (int*)malloc(n * sizeof(int));
    if (arr == NULL) {
        printf("Memory allocation failed!\n");
        return 1;
    }

    printf("Enter %d elements:\n", n);
    for (i = 0; i < n; i++)
        scanf("%d", &arr[i]);

    printf("Enter position to delete: ");
    scanf("%d", &pos);

    // Validate position
    if (pos < 1 || pos > n) {
        printf("Invalid position! Position should be between 1 and %d\n", n);
        free(arr);
        return 1;
    }

    // Shift elements to delete the element at given position
    for (i = pos - 1; i < n - 1; i++)
        arr[i] = arr[i + 1];
    n--;

    printf("Array after deletion:\n");
    for (i = 0; i < n; i++)
        printf("%d ", arr[i]);
    printf("\n");

    // Free allocated memory
    free(arr);
    return 0;
}
