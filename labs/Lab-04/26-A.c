#include <stdio.h>
#include <stdlib.h>

int main() {
    int *arr, n, val, i, j;

    printf("Enter size of sorted array: ");
    scanf("%d", &n);

    // Validate input
    if (n <= 0) {
        printf("Invalid array size!\n");
        return 1;
    }

    // Dynamically allocate memory for n+1 elements (to accommodate insertion)
    arr = (int*)malloc((n + 1) * sizeof(int));
    if (arr == NULL) {
        printf("Memory allocation failed!\n");
        return 1;
    }

    printf("Enter %d sorted elements:\n", n);
    for (i = 0; i < n; i++)
        scanf("%d", &arr[i]);

    printf("Enter value to insert: ");
    scanf("%d", &val);

    // Find the correct position to insert the value to maintain sorted order
    for (i = 0; i < n; i++)
        if (val < arr[i])
            break;

    // Shift elements to make space for insertion
    for (j = n; j > i; j--)
        arr[j] = arr[j - 1];
    arr[i] = val;
    n++;

    printf("Array after insertion:\n");
    for (i = 0; i < n; i++)
        printf("%d ", arr[i]);
    printf("\n");

    // Free allocated memory
    free(arr);
    return 0;
}
