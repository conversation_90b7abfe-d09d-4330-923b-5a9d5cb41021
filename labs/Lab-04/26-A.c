#include <stdio.h>

int main() {
    int arr[100], n, val, i, j;
    printf("Enter size of sorted array: ");
    scanf("%d", &n);
    printf("Enter %d sorted elements:\n", n);
    for (i = 0; i < n; i++)
        scanf("%d", &arr[i]);
    printf("Enter value to insert: ");
    scanf("%d", &val);
    for (i = 0; i < n; i++)
        if (val < arr[i])
            break;
    for (j = n; j > i; j--)
        arr[j] = arr[j - 1];
    arr[i] = val;
    n++;
    printf("Array after insertion:\n");
    for (i = 0; i < n; i++)
        printf("%d ", arr[i]);
    return 0;
}
