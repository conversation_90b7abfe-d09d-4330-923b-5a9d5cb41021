#include <stdio.h>

int main() {
    int arr[100], n, val, i, j;
    printf("Enter size of sorted array: ");
    scanf("%d", &n);
    printf("Enter %d sorted elements:\n", n);
    for (i = 0; i < n; i++)
        scanf("%d", &arr[i]);
    printf("Enter value to delete: ");
    scanf("%d", &val);
    for (i = 0; i < n; i++) {
        if (arr[i] == val) {
            for (j = i; j < n - 1; j++)
                arr[j] = arr[j + 1];
            n--;
            break;
        }
    }
    printf("Array after deletion:\n");
    for (i = 0; i < n; i++)
        printf("%d ", arr[i]);
    return 0;
}
