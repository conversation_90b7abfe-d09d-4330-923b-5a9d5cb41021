// Write a menu driven program to implement following operations on the Doubled
// Ended Queue using an Array
// • Insert at front end, Insert at rear end
// • Delete from front end, Delete from rear end
// • Display all elements of the queue

#include <stdio.h>
#include <stdlib.h>

int *queue;
int front = -1, rear = -1;
int SIZE;

// Function to initialize the deque with dynamic size
int initializeDeque() {
    printf("Enter the size of deque: ");
    scanf("%d", &SIZE);

    if (SIZE <= 0) {
        printf("Invalid deque size!\n");
        return 0;
    }

    queue = (int*)malloc(SIZE * sizeof(int));
    if (queue == NULL) {
        printf("Memory allocation failed!\n");
        return 0;
    }

    printf("Deque of size %d initialized successfully.\n", SIZE);
    return 1;
}

// Function to free the allocated memory
void freeDeque() {
    if (queue != NULL) {
        free(queue);
        queue = NULL;
    }
}

// Insert at rear end
void insertRear(int value) {
    if (rear >= SIZE - 1) {
        printf("Queue Overflow!\n");
        return;
    }
    if (front == -1) {
        front = 0;
    }
    else {
        rear++;
    }
    queue[rear] = value;
    printf("Inserted %d at rear.\n", value);
}

// Insert at front end
void insertFront(int value) {
    if (front == 0) {
        printf("Queue Overflow!\n");
        return;
    }
    if (front == -1) { // empty queue
        front = rear = 0;
    }
    else {
        front--;
    }
    queue[front] = value;
    printf("Inserted %d at front.\n", value);
}

// Delete from front
void deleteFront() {
    if (front == -1) {
        printf("Queue Underflow!\n");
        return;
    }
    printf("Deleted %d from front.\n", queue[front]);
    if (front == rear) {
        front = rear = -1;
    }
    else {
        front++;
    }
}
// Delete from rear
void deleteRear() {
    if (rear == -1) {
        printf("Queue Underflow!\n");
        return;
    }
    printf("Deleted %d from rear.\n", queue[rear]);
    if (front == rear) {
        front = rear = -1;
    }
    else {
        rear--;
    }
}

// Display queue
void display() {
    if (front == -1) {
        printf("Queue Underflow!\n");
        return;
    }
    printf("Queue elements: ");
    int i;
    for (i = front; i <= rear; i++) {
        printf("%d ", queue[i]);
    }
    printf("\n");
}

int main() {
    int choice, value;

    // Initialize the deque with user-defined size
    if (!initializeDeque()) {
        return 1;
    }

    while (1) {
        printf("\n--- Dqueue Menu ---\n");
        printf("1. Insert at Front\n");
        printf("2. Insert at Rear\n");
        printf("3. Delete from Front\n");
        printf("4. Delete from Rear\n");
        printf("5. Display\n");
        printf("6. Exit\n");
        printf("Enter choice: ");
        scanf("%d", &choice);

        switch (choice) {
        case 1:
            printf("Enter value to insert at front: ");
            scanf("%d", &value);
            insertFront(value);
            break;
        case 2:
            printf("Enter value to insert at rear: ");
            scanf("%d", &value);
            insertRear(value);
            break;
        case 3:
            deleteFront();
            break;
        case 4:
            deleteRear();
            break;
        case 5:
            display();
            break;
        case 6:
            freeDeque();
            printf("Exiting program.\n");
            return 0;
        default:
            printf("Invalid choice! Please try again.\n");
        }
    }
    return 0;
}
