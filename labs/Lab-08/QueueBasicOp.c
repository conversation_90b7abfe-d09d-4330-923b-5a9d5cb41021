// Write a menu driven program to implement following operations on the Queue
// using an Array
// • ENQUEUE
// • DEQUEUE
// • DISPLAY

#include <stdio.h>
#include <stdlib.h>

int *queue;
int front = -1, rear = -1;  // Front and rear pointers
int SIZE;

// Function to initialize the queue with dynamic size
int initializeQueue() {
    printf("Enter the size of queue: ");
    scanf("%d", &SIZE);

    if (SIZE <= 0) {
        printf("Invalid queue size!\n");
        return 0;
    }

    queue = (int*)malloc(SIZE * sizeof(int));
    if (queue == NULL) {
        printf("Memory allocation failed!\n");
        return 0;
    }

    printf("Queue of size %d initialized successfully.\n", SIZE);
    return 1;
}

// Function to free the allocated memory
void freeQueue() {
    if (queue != NULL) {
        free(queue);
        queue = NULL;
    }
}

// Function to add element to the queue
void enqueue(int value) {
    if (rear >= SIZE-1) {
        printf("Queue Overflow!\n");
    }
    rear++;
    queue[rear] = value;

    printf("Enqueued: %d\n", value);

    if (front == -1)
        front = 0;

}

// Function to remove element from the queue
int dequeue() {
    if (front == -1) {
        printf("Queue Underflow!\n");
        return 0;
    }
    printf("Dequeued: %d\n", queue[front]);

    if(front==rear){
         front=rear=0;
    }
    else{
         front++;
    }
    return queue[front];
}

// Function to display the elements of the queue
void display() {
    if (front == -1) {
        printf("Queue Underflow.\n");
    }
    else {
        printf("Queue elements: ");
        int i;
        for (i = front; i <= rear; i++) {
            printf("%d ", queue[i]);
        }
        printf("\n");
    }
}

// Main function with menu
int main() {

    int choice, value;

    // Initialize the queue with user-defined size
    if (!initializeQueue()) {
        return 1;
    }

    while (1) {
        printf("\n--- Queue Operations ---\n");
        printf("1. ENQUEUE\n");
        printf("2. DEQUEUE\n");
        printf("3. DISPLAY\n");
        printf("4. EXIT\n");
        printf("Enter your choice: ");
        scanf("%d", &choice);

        switch (choice) {
            case 1:
                printf("Enter value to enqueue: ");
                scanf("%d", &value);
                enqueue(value);
                break;
            case 2:
                dequeue();
                break;
            case 3:
                display();
                break;
            case 4:
                freeQueue();
                printf("Exiting program.\n");
                return 0;
            default:
                printf("Invalid choice! Try again.\n");
        }
    }
    return 0;
}
