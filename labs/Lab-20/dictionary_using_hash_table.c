#include <stdio.h>
#include <stdlib.h>
#include <string.h>

struct DictEntry {
    char key[20];
    char value[50];
};

struct DictEntry *hashTable;
int SIZE;

// Function to initialize the hash table with dynamic size
int initializeHashTable() {
    printf("Enter the size of hash table: ");
    scanf("%d", &SIZE);

    if (SIZE <= 0) {
        printf("Invalid hash table size!\n");
        return 0;
    }

    hashTable = (struct DictEntry*)malloc(SIZE * sizeof(struct DictEntry));
    if (hashTable == NULL) {
        printf("Memory allocation failed!\n");
        return 0;
    }

    // Initialize all entries to empty
    for (int i = 0; i < SIZE; i++) {
        hashTable[i].key[0] = '\0';
        hashTable[i].value[0] = '\0';
    }

    printf("Hash table of size %d initialized successfully.\n", SIZE);
    return 1;
}

// Function to free the allocated memory
void freeHashTable() {
    if (hashTable != NULL) {
        free(hashTable);
        hashTable = NULL;
    }
}

int hashFunction(char key[]) {
    int sum = 0;
    for (int i = 0; key[i] != '\0'; i++){
        sum += key[i];
    }
    return sum % SIZE;
}

void insert(char key[], char value[]) {
    int index = hashFunction(key);

    while (strlen(hashTable[index].key) != 0) {
        index = (index + 1) % SIZE;
    }

    strcpy(hashTable[index].key, key);
    strcpy(hashTable[index].value, value);
}

void get(char key[]) {
    int index = hashFunction(key);
    int start = index;

    while (strlen(hashTable[index].key) != 0) {
        if (strcmp(hashTable[index].key, key) == 0) {
            printf("Value for key '%s' is: %s\n", key, hashTable[index].value);
            return;
        }
        index = (index + 1) % SIZE;

        if (index == start){
            break;
        }
    }

    printf("Key '%s' not found in dictionary.\n", key);
}

void display() {
    printf("\nDictionary Entries:\n");
    for (int i = 0; i < SIZE; i++) {
        if (strlen(hashTable[i].key) != 0) {
            printf("Index %d -> Key: %s, Value: %s\n", i, hashTable[i].key, hashTable[i].value);
        }
    }
}

int main() {
    // Initialize the hash table with user-defined size
    if (!initializeHashTable()) {
        return 1;
    }

    // Insert some sample data
    insert("apple", "A fruit");
    insert("book", "A source of knowledge");
    insert("cat", "A domestic animal");
    insert("dog", "Another domestic animal");

    display();

    // Test retrieval
    get("book");
    get("cat");
    get("fan");  // This key doesn't exist

    // Free allocated memory
    freeHashTable();

    return 0;
}
