#include <stdio.h>
#include <stdlib.h>
#include <time.h>

int main() {
    int *array;
    int *hashTable;
    int i, x, arraySize, hashTableSize;

    // Get array size from user
    printf("Enter the number of elements to generate: ");
    scanf("%d", &arraySize);

    if (arraySize <= 0) {
        printf("Invalid array size!\n");
        return 1;
    }

    // Get hash table size from user
    printf("Enter the size of hash table: ");
    scanf("%d", &hashTableSize);

    if (hashTableSize <= 0) {
        printf("Invalid hash table size!\n");
        return 1;
    }

    // Dynamically allocate memory for array and hash table
    array = (int*)malloc(arraySize * sizeof(int));
    hashTable = (int*)malloc(hashTableSize * sizeof(int));

    if (array == NULL || hashTable == NULL) {
        printf("Memory allocation failed!\n");
        if (array) free(array);
        if (hashTable) free(hashTable);
        return 1;
    }

    // Initialize hash table with zeros
    for(i = 0; i < hashTableSize; i++) {
        hashTable[i] = 0;
    }

    // Generate random numbers
    srand(time(0));
    for(i = 0; i < arraySize; i++) {
        array[i] = (rand() % 900000) + 100000;
    }

    printf("Generated Array:\n");
    for(i = 0; i < arraySize; i++) {
        printf("%d ", array[i]);
    }

    printf("\n\nIndexes Array:\n");
    for(i = 0; i < arraySize; i++) {
        x = (array[i] % (hashTableSize - 2)) + 2;  // Adjust hash function based on table size
        printf("%d ", x);

        // Linear probing for collision resolution
        while(hashTable[x] != 0) {
            x = (x + 1) % hashTableSize;
        }
        hashTable[x] = array[i];
    }

    printf("\n\nHash Table:\n");
    for(i = 0; i < hashTableSize; i++) {
        printf("%d ", hashTable[i]);
    }
    printf("\n");

    // Free allocated memory
    free(array);
    free(hashTable);

    return 0;
}
