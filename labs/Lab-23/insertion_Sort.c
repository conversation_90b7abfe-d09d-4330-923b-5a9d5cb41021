#include<stdio.h>
#include<stdlib.h>

int *arr;
int n;

void insertionSort()
{
	for (int i = 1; i < n; i++)
	{
		int temp = arr[i];
		int j = i-1;

		while (j >= 0 && arr[j] > temp)
		{
			arr[j+1] = arr[j];
			j--;
		}
		arr[j+1] = temp;
	}
}

int main()
{
	printf("Enter the number of elements: ");
	scanf("%d", &n);

	if (n <= 0) {
		printf("Invalid array size!\n");
		return 1;
	}

	// Dynamically allocate memory
	arr = (int*)malloc(n * sizeof(int));
	if (arr == NULL) {
		printf("Memory allocation failed!\n");
		return 1;
	}

	for (int i = 0; i < n; i++)
	{
		printf("Enter Value into arr[%d]: ", i);
		scanf("%d", &arr[i]);
	}

	printf("\nBefore Sorted Array List: ");
	for (int i = 0; i < n; i++)
	{
		printf("%d ", arr[i]);
	}

	insertionSort();

	printf("\n\nAfter Sorted Array List: ");
	for (int i = 0; i < n; i++)
	{
		printf("%d ", arr[i]);
	}
	printf("\n");

	// Free allocated memory
	free(arr);

	return 0;
}
