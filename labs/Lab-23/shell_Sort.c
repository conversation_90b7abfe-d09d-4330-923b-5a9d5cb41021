#include <stdio.h>
#include <stdlib.h>

int *arr;
int n;

void shellSort()
{
	for (int gap = n / 2; gap >= 1; gap = gap / 2)
	{
		for (int j = gap; j < n; j++)
		{
			for (int i = j - gap; i >= 0; i = i - gap)
			{
				if (arr[i + gap] > arr[i])
				{
					break;
				}
				else
				{
					int temp = arr[i + gap];
					arr[i + gap] = arr[i];
					arr[i] = temp;
				}
			}
		}
	}
}

int main()
{
	printf("Enter the number of elements: ");
	scanf("%d", &n);

	if (n <= 0) {
		printf("Invalid array size!\n");
		return 1;
	}

	// Dynamically allocate memory
	arr = (int*)malloc(n * sizeof(int));
	if (arr == NULL) {
		printf("Memory allocation failed!\n");
		return 1;
	}

	for (int i = 0; i < n; i++)
	{
		printf("Enter Value into arr[%d]: ", i);
		scanf("%d", &arr[i]);
	}

	printf("\nBefore Sorted Array List: ");
	for (int i = 0; i < n; i++)
	{
		printf("%d ", arr[i]);
	}

	shellSort();

	printf("\n\nAfter Sorted Array List: ");
	for (int i = 0; i < n; i++)
	{
		printf("%d ", arr[i]);
	}
	printf("\n");

	// Free allocated memory
	free(arr);

	return 0;
}
