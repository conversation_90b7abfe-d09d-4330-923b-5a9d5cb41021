//97. Write a program to implement Counting Sort using Array.

#include <stdio.h>
#include <stdlib.h>

void countingSort(int A[], int B[], int n, int k) {
    // Dynamically allocate count array
    int *c = (int*)calloc(k + 1, sizeof(int));
    if (c == NULL) {
        printf("Memory allocation failed for count array!\n");
        return;
    }

    // count occurrences of each value in A[]
    for (int j = 0; j < n; j++) {
        c[A[j]]++;
    }

    // cumulative count in c[]
    for (int i = 1; i <= k; i++) {
        c[i] += c[i - 1];
    }

    // place elements into sorted positions in B[]
    for (int j = n - 1; j >= 0; j--) {
        B[c[A[j]] - 1] = A[j];
        c[A[j]]--;
    }

    free(c);
}

int main() {
    int *A, *B, n, k = 0;

    printf("Enter number of elements: ");
    scanf("%d", &n);

    if (n <= 0) {
        printf("Invalid array size!\n");
        return 1;
    }

    // Dynamically allocate arrays
    A = (int*)malloc(n * sizeof(int));
    B = (int*)malloc(n * sizeof(int));

    if (A == NULL || B == NULL) {
        printf("Memory allocation failed!\n");
        if (A) free(A);
        if (B) free(B);
        return 1;
    }

    printf("Enter %d elements (positive integers):\n", n);
    for (int i = 0; i < n; i++) {
        scanf("%d", &A[i]);
        if (A[i] > k)
            k = A[i]; // Find the maximum value
    }

    countingSort(A, B, n, k);

    printf("Sorted array:\n");
    for (int i = 0; i < n; i++) {
        printf("%d ", B[i]);
    }
    printf("\n");

    free(A);
    free(B);
    return 0;
}
