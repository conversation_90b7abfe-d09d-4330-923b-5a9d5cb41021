#include <stdio.h>
#include <stdlib.h>

int longestSubarrayWithSumK(int arr[], int n, int k) {
    int start = 0, end = 0;
    int sum = 0;
    int maxLen = 0;

    while (end < n) {
        sum += arr[end];

        while (sum > k && start <= end) {
            sum -= arr[start];
            start++;
        }

        int windowLength = end - start + 1;
        if (sum <= k && windowLength > maxLen) {
            maxLen = windowLength;
        }

        end++;
    }

    return maxLen;
}

int main() {
    int n, k;

    printf("Enter number of elements: ");
    scanf("%d", &n);

    if (n <= 0) {
        printf("Invalid array size!\n");
        return 1;
    }

    int *arr = (int*)malloc(n * sizeof(int));
    if (arr == NULL) {
        printf("Memory allocation failed!\n");
        return 1;
    }

    printf("Enter %d elements: ", n);
    for (int i = 0; i < n; i++) {
        scanf("%d", &arr[i]);
    }

    printf("Enter the target sum k: ");
    scanf("%d", &k);

    int result = longestSubarrayWithSumK(arr, n, k);
    printf("Length of longest subarray with sum <= %d is: %d\n", k, result);

    free(arr);
    return 0;
}
