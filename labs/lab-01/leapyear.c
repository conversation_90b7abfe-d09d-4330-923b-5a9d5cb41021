// 5. Leap year
#include <stdio.h>
void main() {
    int year;
    printf("Enter a year: ");
    scanf("%d", &year);

    if ((year % 4 == 0 && year % 100 != 0) || year % 400 == 0) {
        printf("%d is a leap year", year);
    } else {
        printf("%d is not a leap year", year);
    }
}

// or using nested if as below

// #include <stdio.h>
// int main() {
//     int year;
//     printf("Enter a year: ");
//     scanf("%d", &year);

//     if (year % 4 == 0) {
//         if (year % 100 == 0) {
//             if (year % 400 == 0) {
//                 printf("%d is a leap year.\n", year);
//             } else {
//                 printf("%d is not a leap year.\n", year);
//             }
//         } else {
//             printf("%d is a leap year.\n", year);
//         }
//     } else {
//         printf("%d is not a leap year.\n", year);
//     }

//     return 0;
// }
