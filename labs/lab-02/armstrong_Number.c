// #include <stdio.h>
// #include <math.h>

// void main()
// {
//     int num;
//     int sum = 0;
//     int arm;
//     int length = 0;

//     printf("Enter Number: ");
//     scanf("%d", &num);

//     arm = num;

//     // Step 1: Count number of digits
//     int temp = num;
//     while (temp > 0) {
//         length++;
//         temp = temp / 10;
//     }

//     // Step 2: Calculate sum of digits raised to 'len'
//     temp = num;
//     while (temp > 0) {
//         int r = temp % 10;
//         sum = sum + pow(r, len);
//         temp = temp / 10;
//     }

//     // Step 3: Check if Armstrong
//     if (sum == arm)
//         printf("%d is an Armstrong Number\n", arm);
//     else
//         printf("%d is Not an Armstrong Number\n", arm);
// }


//Lab2 -- 15C
#include <stdio.h>
#include <math.h>

int armstrong(int);
int len(int);

void main(){
    int i =1;
    while(i<=1000){
        int isArmStrong = armstrong(i);
        if(isArmStrong == 1)
        printf("%d is armStrong\n",i);
        i++;
    }
}

int len(int n){
    int i=0;
    for(i=0;n!=0;i++,n/=10){}
    return i;
}

int armstrong(int n){
    int dummy = n;
    int sum = 0;
    int length = len(n);
    while(dummy>0){
        sum+= pow(dummy%10,length);
        dummy/=10;
    }
    return (sum == n?1:0);
}