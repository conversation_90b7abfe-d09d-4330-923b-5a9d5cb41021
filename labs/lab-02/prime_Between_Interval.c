#include <stdio.h>

void main() {
    int num1;
    int num2;
    int count;

    printf("Enter First Number = ");
    scanf("%d", &num1);

    printf("Enter Second Number = ");
    scanf("%d", &num2);

    for (int i = num1; i <= num2; i++) {
        count = 0;

        for (int j = 2; j < i ; j++) {
            if (i % j == 0) {
                count = 1; // not a prime
                break;
            }
        }

        if (count == 0)
            printf("%d is a Prime\n", i);
        else
            printf("%d is Not a Prime\n", i);
    }
}
