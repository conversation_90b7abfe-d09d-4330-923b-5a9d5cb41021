#include <stdio.h>
#include <ctype.h>
#include <string.h> // for strcpy

int top = -1;

void push( int arr[], int size, int val);
int pop( int arr[]);
int peep( int arr[], int size, int n);
int isOpr(char c);
int inputPrecedence(char c);
int stackPrecedence(char c);
int rank(char c);
void postfix(char infix[]);
int validateInfix(char infix[]);

void main(){
         char infix[1000];
         char *bracket= ")";
         printf("Enter infix String following given rules : \n");
         printf("1) Operators, operand and should not be space-seperated \n");
         printf("2) numbers should be between 0 to 9 \n");
         scanf("%s",infix);
         if(!validateInfix(infix)){
            printf("WRONG FORMAT FOR INFIX!!");
            return;
         }
         strcat(infix,bracket);
         postfix(infix);
}

int validateInfix(char infix[]) {
    int open = 0;
    int expectOperand = 1;

    for (int i = 0; infix[i] != '\0'; i++) {
        char c = infix[i];

        if (c = ' ') continue;

        if (isalnum(c)) {
            if (!expectOperand) return 0;
            expectOperand = 0;
        } else if (c == '(') {
            open++;
            expectOperand = 1;
        } else if (c == ')') {
            open--;
            if (open < 0 || expectOperand) return 0;
            expectOperand = 0;
        } else if (isOpr(c)) {
            if (expectOperand) return 0;
            expectOperand = 1;
        } else {
            return 0;
        }
    }

    return (open == 0 && !expectOperand);
}

void postfix(char infix[]){
        int len = strlen(infix);
        
        int S[len]; // Stack used for operator precedence
        char rev_polish[len+1]; // Resulting array of string for  rev_polish/postfix
        int r = 0; // rank tracing
        int rev_polishInd = 0; // Keep track of new rev_polish index
        char temp = '('; // used to add operators in string.
        
        // addding initial starting bracket to stack
        push(S,len,temp);
    
        // Iterate through each letter
        int i=0;
        while(i < len){
                if(top < 0){
                printf("\nSTACK EMPTY :: INVALID INFIX STRING\n");
                return;
                }
    
            // as long as precedence of stack is higher, pop and add to rev_polish array
            while( stackPrecedence(peep(S,len,1)) > inputPrecedence(infix[i]) ){
                temp = (char)pop(S);
                rev_polish[rev_polishInd++] = temp;
                r += rank(temp); // update rank as per need
                if(r<1){ // rank can't go below 1!!
                    printf("\nRANK IS LOW :: INVALID INFIX STRING\n");
                    return;
                }
            }
            
            if( stackPrecedence(peep(S,len,1)) != inputPrecedence(infix[i]) ){
                push(S,len,infix[i]); // if current element is a operator
            }
            else pop(S);   // paranthesis tackled
             i++;
             
        }   // completed whole string
        
        // stack must be empty and rank should maintain 1
        if(top!=-1 || r != 1){
            printf("\nERROR :: RANK IS UNBALANCED :: INVALID INFIX STRING\n");
            return;
        }
             
        //print postfix string 
        rev_polish[rev_polishInd] = '\0';
        printf("%s", rev_polish);
        
        return; 
    }
  
void push( int arr[], int size, int val){
    if(top == size - 1) {
        printf("\nStack overflow!!\n");
        return;
    }
    top++;
    arr[top] = val;
}

int pop( int arr[]){
    if(top == -1) {
        printf("\nStack underflow!!\n");
        return -1;
    }
    top--;
    return arr[top+1];
}

int peep( int arr[], int size, int n){
    if(top + 1 < n) {
        printf("\nStack underflow!!\n");
        return 0;
    }
    
    return arr[top - n + 1];
}

int isOpr(char c){
    return ( c=='*' || c=='/' || c=='+' || c=='-' || c=='^' );
}

int inputPrecedence(char c){

    if(c>='0' && c<='9') return 7;
    if(c == '+' || c=='-') return 1;
    if(c == '*' || c=='/') return 3;
    if(c == '^') return 6;
    if(c =='(' ) return 9;
    return 0; 
 }
 
int stackPrecedence(char c){
    if(c>='0' && c<='9') return 8;
     if(c == '+' || c=='-') return 2;
     if(c == '*' || c=='/') return 4;
     if(c == '^') return 5;
     return 0; 
    }

int rank(char c){
        if(c =='(' || c==')') return 0;
        if(c>='0' && c<='9') return 1;
        return -1; 
    }

  
