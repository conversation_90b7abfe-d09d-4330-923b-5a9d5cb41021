#include <stdio.h>
#include <string.h> // for strcpy

int top = -1;

void push( char arr[], int size, char val){
    if(top == size - 1) {
        printf("\nStack overflow!!\n");
        return;
    }
    top++;
    arr[top] = val;
}

char pop( char arr[]){
    if(top == -1) {
        printf("\nStack underflow!!\n");
        return -1;
    }
    top--;
    return arr[top+1];
}

char peep( char arr[], int size, int n){
    if(top + 1 < n) {
        printf("\nStack underflow!!\n");
        return 0;
    }
    
    return arr[top - n + 1];
}

int isNum(char c[]){
    int len = strlen(c);
    for(int i=0;i<len;i++){
        if(c[i]<'0' || c[i]>'9') return 0;
    } 
    return 1;
}

int inputPrecedence(char c){

    if(c == '+' || c=='-') return 1;
    if(c == '*' || c=='/') return 3;
    if(c == '^') return 5;
    if(c =='(' ) return 9;
    return 0; 
 }
 
int stackPrecedence(char c){
     if(c == '+' || c=='-') return 2;
     if(c == '*' || c=='/') return 4;
     if(c == '^') return 6;
     return 0; 
    }

int rank(char c){
        if(c =='(' || c==')') return 0;
        return -1; 
     }


void prefix(char* infix){
    printf("infix : %s\n",infix);

    int len = strlen(infix);

    // reverse infix but, don't forget to change '(' and ')'
    for(int i=0;i<len/2;i++){
        if(infix[i] =='(')
            infix[i] = ')';
        else if(infix[i] ==')')
            infix[i] = '(';
            
        char temp = infix[i];
        infix[i] = infix[len - 1 - i];

        if(infix[i] =='(')
            infix[i] = ')';
        else if(infix[i] ==')')
            infix[i] = '(';

        infix[len - 1 - i] = temp;
    }

    printf("reversed infix : %s\n",infix);

    char S[len]; // stack to store operators
    char polish[len+1][len]; // polish string array
    int r = 0; // rank
    int polishInd = 0; // polishIndex
    char temp[2] = "("; // used to copy operator and brackets to polish
    
    push(S,len,temp[0]);
    int i=0;

    // Iterating through characters
    while(i < len){
        char c[len];
        int ind = 0;
        while(i<len && infix[i]!=' ' ){
            c[ind++] = infix[i];
            i++;
        }
        c[ind] = '\0';
        
        i++;
        
        if(isNum(c)){
            strcpy(polish[polishInd++],c);
            r++;
        }
        else {
            if(top < 0){
            printf("\nSTACK EMPTY :: INVALID INFIX STRING\n");
            return;
            }
        while( stackPrecedence(peep(S,len,1)) > inputPrecedence(c[0]) ){
            temp[0] = pop(S);
            strcpy(polish[polishInd++],temp);
            r += rank(temp[0]);
            if(r<1){
                printf("\nRANK IS LOW :: INVALID INFIX STRING\n");
                return;
            }
        }
        
        if( stackPrecedence(peep(S,len,1)) != inputPrecedence(c[0]) )
        push(S,len,c[0]); // operator
        else pop(S);   // paranthesis tackled
    }
    }
    
    if(top!=-1 || r != 1){
        printf("\nERROR :: RANK IS UNBALANCED :: INVALID INFIX STRING\n");
        return;
    }
    
    for( i=0;i<polishInd/2;i++){
        // swapping strings in array.
        char temp[len];
        strcpy(temp,polish[i]);
        strcpy(polish[i], polish[polishInd - 1 - i]);
        strcpy(polish[polishInd - 1 - i],temp);
    }
    
    for(int i=0;i<polishInd;i++){
        printf("%s ", polish[i]);
    }
}

void main(){
    char infix[1002];
    char bracket= '(';
    char space= ' ';
    printf("Enter infix String following given rules : \n");
    printf("1) Operators, operand and should be space-seperated \n");
    printf("2) No more than single space should be used to seperate\n");
    scanf("%[^\n]s",infix);
    int len = strlen(infix);
    
    // adding bracket at front
    int i;
    for(i=0;i<len;i+=2){
        char temp = infix[i];
        infix[i] = bracket;
        bracket = temp;

        temp = infix[i+1];
        infix[i+1] = space;
        space = temp;
        
    }
    
    // don't forget \0 or string will go random.
    if(len%2!=0){ 
        infix[i] = bracket;
        infix[i + 1] = space;
        infix[i + 2] = '\0';
    }
    else {
        infix[i] = space;
        infix[i + 1] = bracket;
        infix[i + 2] = '\0';
    }
    
    prefix(infix);
    
}