#include <stdio.h>
#include <stdlib.h> // for atoi() function
#include <string.h>
#include <math.h>

#define MAX 100

int stack[MAX];
int top = -1;

void push(int val) {
    if (top == MAX - 1) {
        printf("Stack overflow\n");
        exit(1);
    }
    stack[++top] = val;
}
int pop() {
    if (top == -1) {
        printf("Stack underflow\n");
        exit(1);
    }
    return stack[top--];
}

int isOperator(char c) {
    return c == '+' || c == '-' || c == '*' || c == '/' || c == '^';
}

int evaluatePostfix(char expr[]) {
    int i = 0, k = 0;
    char token[20]; // Buffer to hold one token (number or operator)
    
    while (1) {
        // Skip spaces
        while (expr[i] == ' ') i++;
        
        // If end of string, break
        if (expr[i] == '\0') break;
        
        // Extract token till next space or end of string
        k = 0;
        while (expr[i] != ' ' && expr[i] != '\0') {
            token[k++] = expr[i++];
        }
        token[k] = '\0';  // Null-terminate token
// Now analyze token
        if (strlen(token) == 1 && isOperator(token[0])) {
            // Operator token
            int val2 = pop();
            int val1 = pop();
            int res = 0;
            
            switch (token[0]) {
                case '+': res = val1 + val2; break;
                case '-': res = val1 - val2; break;
                case '*': res = val1 * val2; break;
                case '/': 
                    if (val2 == 0) {
                        printf("Division by zero error\n");
                        exit(1);
                    }
                    res = val1 / val2;
                    break;
                case '^': res = (int)pow(val1, val2); 
                    break;
                default:
                    printf("Invalid operator: %s\n", token);
                    exit(1);
            }
            push(res);
        } else {
            // Operand token (supports negative and multi-digit numbers)
            int num = atoi(token); // convert Ascii to interger
            push(num);
        }
    }

    return pop();
}

int main() {
    char postfix[MAX];
    printf("Enter postfix expression (space-separated): ");
    scanf(" %[^\n]s", postfix);  // Read until newline, including spaces

    int result = evaluatePostfix(postfix);
    printf("Result: %d\n", result);

    return 0;
}
