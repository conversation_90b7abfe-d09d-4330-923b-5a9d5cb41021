#include <stdio.h>
#include <stdlib.h>

struct node {
    int data;
    struct node* next;
};

// Function to insert at end
struct node* insertAtEnd(struct node* head, int value) {
    struct node* newNode = (struct node*)malloc(sizeof(struct node));
    newNode->data = value;
    newNode->next = NULL;

    if (head == NULL)
        return newNode;

    struct node* temp = head;
    while (temp->next != NULL)
        temp = temp->next;

    temp->next = newNode;
    return head;
}

// Function to remove duplicates
void removeDuplicates(struct node* head) {
    struct node *current = head, *save = NULL, *temp = NULL;

    while (current != NULL && current->next != NULL) {
        save = current;
        while (save->next != NULL) {
            if (current->data == save->next->data) {
                temp = save->next;
                save->next = save->next->next;
                free(temp);
            } else {
                save = save->next;
            }
        }
        current = current->next;
    }
}

// Display function
void display(struct node* head) {
    if (head == NULL) {
        printf("List is empty.\n");
        return;
    }

    struct node* temp = head;
    printf("Linked List: ");
    while (temp != NULL) {
        printf("%d -> ", temp->data);
        temp = temp->next;
    }
    printf("NULL\n");
}

int main() {
    struct node* head = NULL;
    int n, value;

    printf("Enter number of nodes: ");
    scanf("%d", &n);

    for (int i = 1; i <= n; i++) {
        printf("Enter value for node %d: ", i);
        scanf("%d", &value);
        head = insertAtEnd(head, value);
    }

    printf("\nOriginal List:\n");
    display(head);

    removeDuplicates(head);

    printf("\nList after removing duplicates:\n");
    display(head);

    return 0;
}
