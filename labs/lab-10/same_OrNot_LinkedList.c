#include <stdio.h>
#include <stdlib.h>

struct node
{
    int data;
    struct node *next;
};

// Insert at beginning
struct node *insertAtFirst(struct node *head)
{
    struct node *newNode;
    newNode = (struct node *)malloc(sizeof(struct node));

    if (newNode == NULL)
    {
        printf("Memory allocation failed.\n");
        return head;
    }

    printf("Enter data you want to insert at first: ");
    scanf("%d", &newNode->data);

    newNode->next = head;
    head = newNode;

    return head;
}

// Insert at end
struct node *insertAtEnd(struct node *head)
{
    struct node *newNode;
    newNode = (struct node *)malloc(sizeof(struct node));
    if (newNode == NULL)
    {
        printf("Memory allocation failed.\n");
        return head;
    }

    printf("Enter data you want to insert at end: ");
    scanf("%d", &newNode->data);
    newNode->next = NULL;

    if (head == NULL)
    {
        head = newNode;
    }
    else
    {
        struct node *temp = head;
        while (temp->next != NULL)
        {
            temp = temp->next;
        }
        temp->next = newNode;
    }

    return head;
}

// Display
void display(struct node *head)
{
    if (head == NULL)
    {
        printf("List is empty.\n");
        return;
    }

    struct node *temp = head;
    printf("Linked List: ");
    while (temp != NULL)
    {
        printf("%d -> ", temp->data);
        temp = temp->next;
    }
    printf("NULL\n");
}

// Compare two linked lists
int compareLists(struct node *head1, struct node *head2)
{
    while (head1 != NULL && head2 != NULL)
    {
        if (head1->data != head2->data)
        {
            return 0; // Not same
        }
        head1 = head1->next;
        head2 = head2->next;
    }

    if (head1 == NULL && head2 == NULL)
        return 1; // Same

    return 0; // Length mismatch
}

int main()
{
    struct node *head1 = NULL;
    struct node *head2 = NULL;

    int choice, listChoice;

    do
    {
        printf("\nMain Menu:\n");
        printf("1. Insert at Beginning\n");
        printf("2. Insert at End\n");
        printf("3. Display\n");
        printf("4. Compare Two Linked Lists\n");
        printf("0. Exit\n");

        printf("Enter your choice: ");
        scanf("%d", &choice);

        if (choice >= 1 && choice <= 3)
        {
            printf("Which List (1 or 2)? ");
            scanf("%d", &listChoice);
        }

        switch (choice)
        {
        case 1:
            if (listChoice == 1)
                head1 = insertAtFirst(head1);
            else if (listChoice == 2)
                head2 = insertAtFirst(head2);
            else
                printf("Invalid list choice.\n");
            break;

        case 2:
            if (listChoice == 1)
                head1 = insertAtEnd(head1);
            else if (listChoice == 2)
                head2 = insertAtEnd(head2);
            else
                printf("Invalid list choice.\n");
            break;

        case 3:
            if (listChoice == 1)
            {
                printf("List 1:\n");
                display(head1);
            }
            else if (listChoice == 2)
            {
                printf("List 2:\n");
                display(head2);
            }
            else
                printf("Invalid list choice.\n");
            break;

        case 4:
            if (compareLists(head1, head2))
                printf("Both linked lists are the SAME.\n");
            else
                printf("Linked lists are NOT the same.\n");
            break;

        case 0:
            printf("Exiting...\n");
            break;

        default:
            printf("Invalid choice! Try again.\n");
        }

    } while (choice != 0);

    return 0;
}
