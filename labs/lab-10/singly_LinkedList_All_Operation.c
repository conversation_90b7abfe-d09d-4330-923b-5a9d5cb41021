#include <stdio.h>
#include <stdlib.h>

struct node
{
    int data;
    struct node *next;
};

struct node *head = 0;

// Function to insert a node at the beginning
void insertAtFirst()
{
    struct node *newNode;
    
    //struct node *
    //It converts a void * (generic pointer) into a pointer of type struct node *.
    //Converts the void * returned by malloc() to (struct node *)
    
    //malloc
    //Takes the number of bytes to allocate as an argument.
    //Returns a void * (a generic pointer).
    //Dynamically allocates a block of memory during runtime.
    
    //sizeof(struct node *)
    //calculates how many bytes are needed.
    
    newNode = (struct node *)malloc(sizeof(struct node));

    printf("Enter data you want to insert at first: ");
    scanf("%d", &newNode->data);

    newNode->next = head;
    head = newNode;
}

void insertAtEnd()
{
    struct node *newNode;
    newNode = (struct node *)malloc(sizeof(struct node));
    
    struct node *temp;

    printf("Enter data you want to insert at end: ");
    scanf("%d", &newNode->data);

    newNode->next = 0;

    temp = head;

    while (temp->next != 0)
    {
        temp = temp->next;
    }
    temp->next = newNode;
}

void deleteFromFirst()
{
    struct node *temp;

    temp = head;

    if (head == 0)
    {
        printf("Linked List is Empty\n");
    }
    else
    {
        printf("Deleted Node is:%d", head->data);
        head = head->next;
        free(temp);
    }
}

void deleteFromEnd()
{

    if (head == 0)
    {
        printf("Linked List is Empty\n");
        return;
    }

    struct node *prevNode = head;
    struct node *nextNode = head->next;

    if (head->next == 0)
    {
        printf("Deleted Node is:%d", head->data);
        free(head);
        head = 0;
    }

    while (nextNode->next != 0)
    {
        prevNode = nextNode;
        nextNode = nextNode->next;
    }
    printf("Deleted Node is:%d", nextNode->data);
    prevNode->next = 0;
    free(nextNode);
}

void deleteFromPosition()
{
    struct node *prevNode;
    struct node *nextNode;

    if (head == 0)
    {
        printf("List Is Empty\n");
        return; 
    }

    int pos, i = 1;

    prevNode = head;

    printf("Enter on Which position You want to delete Node From List:");
    scanf("%d", &pos);

    
    if (pos == 1)
    {
        nextNode = head;
        head = head->next;
        free(nextNode);
        printf("Node deleted from position 1\n");
        return;
    }
    
    if (pos <= 0)
    {
        printf("Invalid position!\n");
        return;
    }
    
    while (i < pos - 1)
    {
        prevNode = prevNode->next;
        i++;
    }
    nextNode = prevNode->next;
    prevNode->next = nextNode->next;
    free(nextNode);
}

// Function to display the linked list
void display()
{
    struct node *temp = head;
    printf("Linked List: ");
    while (temp != 0)
    {
        printf("%d -> ", temp->data);
        temp = temp->next;
    }
    printf("NULL\n");
}

void countNodes()
{
    int count = 0;

    if (head == NULL)
    {
        printf("List is Empty\n");
    }
    else
    {
        struct node* temp;
        temp = head;
        
        while (temp != NULL)
        {
            count++;
            temp = temp->next;
        }
    }
    printf("Total Number of Nodes in List is:%d",count);
}

void main()
{
    int choice;

    do
    {
        printf("\nMenu:\n");
        printf("Press 1 For Insert Node at Beginning\n");
        printf("Press 2 For Insert Node at End\n");
        printf("Press 3 For Delete Node From First\n");
        printf("Press 4 For Delete Node From End\n");
        printf("Press 5 For Delete Node From Specified Position\n");
        printf("Press 6 For Count Total Node in List\n");
        printf("Press 7 For Display\n");
        printf("Press 0 For Exit\n");

        printf("Enter your choice: ");
        scanf("%d", &choice);

        switch (choice)
        {
        case 1:
            insertAtFirst();
            break;

        case 2:
            insertAtEnd();
            break;

        case 3:
            deleteFromFirst();
            break;
        case 4:
            deleteFromEnd();
            break;
        case 5:
            deleteFromPosition();
            break;
        case 6:
            countNodes();
            break;
        case 7:
            display();
            break;
        case 0:
            printf("Exiting...\n");
            break;

        default:
            printf("Invalid choice. Try again.\n");
        }
    } while (choice != 0);
}
