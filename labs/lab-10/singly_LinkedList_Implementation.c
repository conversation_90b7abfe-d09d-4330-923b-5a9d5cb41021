#include <stdio.h>
#include <stdlib.h> 

struct node
{
	int data;
	struct node *next;
};

int main()
{
	struct node *head = NULL, *newNode, *temp;
	int choice;

	do
	{
		newNode = (struct node *)malloc(sizeof(struct node));

		printf("Enter Data: ");
		scanf("%d", &newNode->data);

		newNode->next = NULL;

		if (head == NULL)
		{
			head = temp = newNode;
		}
		else
		{
			temp->next = newNode;
			temp = newNode;
		}

		printf("Do you want to continue? (1 for Yes / 0 for No): ");
		scanf("%d", &choice);

	} while (choice != 0);

	printf("\nLinked List Elements:\n");
	temp = head;

	while (temp != NULL)
	{
		printf("%d -> ", temp->data);
		temp = temp->next;
	}
	printf("NULL\n");

	return 0;
}
