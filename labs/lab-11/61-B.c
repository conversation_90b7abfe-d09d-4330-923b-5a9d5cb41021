#include <stdio.h>
#include <stdlib.h>
#include "../node.h"

void enqueue(struct node** first, int val){
    if(*first == NULL){
        *first = (struct node*)malloc(sizeof(struct node));
        if(!(*first)){
        printf("\n MEMORY ALLOCATION FAILED \n");
        return;
        }
        (*first)->val = val;
        (*first)->next = NULL;
        return;
    }
    struct node* temp = *first;
    while(temp->next !=NULL){
        temp = temp->next;
    }
    temp->next = (struct node*)malloc(sizeof(struct node));
    if(!(temp->next)){
        printf("\n MEMORY ALLOCATION FAILED \n");
        return;
    }
    temp->next->val = val;
    temp->next->next = NULL;
}

int dequeue(struct node** first){
    if((*first) == NULL) return 0;
    if( (*first)->next == NULL){
        int val = (*first)->val; 
        *first = NULL;
        return val;
    }
    int val = (*first)->val;
    struct node* temp = (*first)->next;
    free((*first));
    *first = temp;
    return val;
}

void displayNode(struct node** first, char* format){
    if((*first) == NULL ){
        printf("\nQUEUE IS EMPTY\n");
        return;
    }
    printf("\n");
    struct node* temp = (*first);
    while(temp!=NULL){
        printf(format,temp->val);
        temp = temp->next;
    }
    printf("\n");
}

void main(){
    struct node **first = (struct node **)malloc(sizeof(struct node *) );
    *first = NULL;
    int opr = 0;
    int val;
    do {
        printf("Select operation to perform on queue : \n");
        printf("0) TERMINATE CODE\n");
        printf("1) ENQUEUE\n");
        printf("2) DEQUEUE\n");
        printf("3) DISPLAY\n");
        scanf("%d",&opr);

        switch (opr)
        {
        case 1:
            printf("Enter number : \n");
            scanf("%d",&val);
            enqueue(first,val);
            break;
        case 2:
            printf("\nRemoved %d from queue\n",dequeue(first));
            break;
        case 3:
            displayNode(first,"| %d |");
            break;
        
        default:
            break;
        }
    } while(opr);
}