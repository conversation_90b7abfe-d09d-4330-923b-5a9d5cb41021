#include <stdio.h>
#include <stdlib.h>

struct node {
    int INFO;
    struct node* LINK;
};

struct node* insertAtLast(struct node* FIRST, int val){

    struct node* NEW = (struct node*)malloc(sizeof(struct node));
    NEW->INFO = val;
    NEW->LINK = NULL;

    if(FIRST == NULL){
        return NEW;
    }

    struct node* SAVE = FIRST;
    while(SAVE->LINK !=NULL){
        SAVE = SAVE->LINK;
    }

    SAVE->LINK = NEW;
    return FIRST;

}

void displayNode(struct node* FIRST){
    if(FIRST == NULL ){
        printf("\nLINKED LIST IS EMPTY\n");
        return;
    }
    
    printf("\n");
    struct node* NEW = FIRST;
    while(NEW!=NULL){
        printf("%d ",NEW->INFO);
        NEW = NEW->LINK;
    }
    printf("\n");
}

int find(struct node* FIRST, struct node* LAST){
    while(FIRST!=LAST){
        if(FIRST->INFO == LAST->INFO) return 1;
        FIRST = FIRST->LINK;
    }
    return 0;
}

void removeDuplicate(struct node* FIRST){
    if(FIRST == NULL || FIRST->LINK == NULL) return;

    struct node* SAVE = FIRST;
    
    while(SAVE->LINK != NULL ){
        int numberExists = 0;
        
        if( find(FIRST,SAVE->LINK) ){
            SAVE->LINK = SAVE->LINK->LINK;
        }
        else {
            SAVE = SAVE->LINK;
        }
    }

}

void main(){
    struct node *FIRST = NULL;
    int opr = 0;
    int val;

    do {
        printf("Enter number : \n");
        scanf("%d",&val);
        
        FIRST = insertAtLast(FIRST, val);
        
        printf("Do you want add more? 1)YES  0)NO \n");
        scanf("%d",&opr);
        
    } while(opr);
    
    printf("Given list :\n");
    displayNode(FIRST);

    removeDuplicate(FIRST);
    printf("Duplicates removed list :\n");
    displayNode(FIRST);
}