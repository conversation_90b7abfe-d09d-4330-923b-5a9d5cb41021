#include <stdio.h>
#include <stdlib.h>

// Structure of a node
struct node {
    int info;
    struct node *link;
};

// Insert a node at the end of the list
struct node* insert(struct node *first, int val) {
    struct node *new_node = (struct node*)malloc(sizeof(struct node));
    new_node->info = val;
    new_node->link = NULL;

    if (first == NULL) {
        first = new_node;
    } else {
        struct node *temp = first;
        while (temp->link != NULL) {
            temp = temp->link;
        }
        temp->link = new_node;
    }
    return first;
}

// Function to print the linked list
void printList(struct node *first) {
    struct node *save = first;
    printf("Linked List: ");
    while (save != 0)
    {
    printf("%d -> ", save->info);
    save = save->link;
    }
    printf("NULL\n");
}


// Function to copy a linked list using the given algorithm
struct node* copyList(struct node *first) {
    if (first == NULL) {
        return NULL;
    }

    struct node *avail, *new_node, *begin = NULL;
    struct node *save, *pred;

    // Step 1: Allocate memory for the first copied node
    new_node = (struct node*)malloc(sizeof(struct node));
    if (new_node == NULL) {
        printf("Underflow\n");
        return NULL;
    }

    // Step 2: Set data and link for the first copied node
    new_node->info = first->info;  // copy info from original first node
    new_node->link = NULL;         // set link of new node to NULL
    begin = new_node;              // set begin pointer to new list's first node
            
    save = first;  // Save is used to traverse the original list

    // Step 3: Copy rest of the nodes
    while (save->link != NULL) {
        pred = new_node;        // pred points to the last node in the new list
        save = save->link;      // move to the next node in the original list

        // Allocate memory for a new node
        avail = (struct node*)malloc(sizeof(struct node));
        if (avail == NULL) {
            printf("Underflow\n");
            return NULL;
        }

        // Set data and link for the new node
        new_node = avail;                // move new_node to this newly allocated space
        new_node->info = save->info;     // copy data from current node of original list
        new_node->link = NULL;           // set next to NULL (will be linked shortly)

        pred->link = new_node;           // link the previous new node to the current new node
    }

    return begin;  // return pointer to the head of the copied list
}

int main() {
    struct node *first = NULL;
    int val;

    printf("Enter integers one by one (-1 to stop):\n");

    // Taking user input one-by-one
    while (1) {
        scanf("%d", &val);
        if (val == -1) break;
        first = insert(first, val);
    }

    printf("\nOriginal ");
    printList(first);

    // Copy the list
    struct node *copied = copyList(first);

    printf("Copied ");
    printList(copied);

    return 0;
}