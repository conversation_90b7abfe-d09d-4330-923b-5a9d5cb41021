#include <stdio.h>
#include <stdlib.h>

// Define the node structure
struct node {
    int info;
    struct node *link;
};

// Insert a new node at the end of the list
struct node* insert(struct node* first, int val) {
    struct node* newNode = (struct node*)malloc(sizeof(struct node));
    newNode->info = val;
    newNode->link = NULL;

    if (first == NULL) {
        first = newNode;
    } else {
        struct node* temp = first;
        while (temp->link != NULL) {
            temp = temp->link;
        }
        temp->link = newNode;
    }

    return first;
}

// Reverse the linked list
struct node* reverseList(struct node *first) {
    struct node *prev = NULL, *curr = first, *next = NULL;
    while (curr != NULL) {

        next = curr->link;  // store next node
        curr->link = prev;  // reverse link
        prev = curr;        // move prev forward
        curr = next;        // move curr forward

    }
    return prev;
}

// Function to print the linked list
void printList(struct node *first) {
    struct node *save = first;
    printf("Linked List: ");
    while (save != 0)
    {
    printf("%d -> ", save->info);
    save = save->link;
    }
    printf("NULL\n");
}

int main() {
    struct node *first = NULL;
    int num;

    printf("Enter integers one by one (-1 to stop):\n");

    while (1) {
        scanf("%d", &num);
        if (num == -1) break;
        first = insert(first, num);
    }

    printf("Original list: ");
    printList(first);

    first = reverseList(first);

    printf("Reversed list: ");
    printList(first);

    return 0;
}
