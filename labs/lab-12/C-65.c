#include <stdio.h>
#include <stdlib.h>

struct Node {
    int info;
    struct Node* link;
};

// Insert node at end and return updated list
struct Node* insert(struct Node* first, int value) {
    struct Node* newNode = (struct Node*)malloc(sizeof(struct Node));
    newNode->info = value;
    newNode->link = NULL;

    if (first == NULL) return newNode;

    struct Node* temp = first;
    while (temp->link) temp = temp->link;
    temp->link = newNode;
    return first;
}

// Function to print the linked list
void printList(struct Node *first) {
    struct Node *save = first;
    printf("Linked List: ");
    while (save != NULL) {
        printf("%d -> ", save->info);
        save = save->link;
    }
    printf("NULL\n");
}


// Count total nodes
int countNodes(struct Node* first) {
    int count = 0;
    while (first) {
        count++;
        first = first->link;
    }
    return count;
}

// Swap kth node from beginning and end
struct Node* swapKth(struct Node* first, int k) {
    int n = countNodes(first);
    if (k > n || k <= 0 || 2 * k - 1 == n) return first;

    struct Node *prev1 = NULL, *curr1 = first;
    for (int i = 1; i < k; i++) {
        prev1 = curr1;
        curr1 = curr1->link;
    }

    struct Node *prev2 = NULL, *curr2 = first;
    for (int i = 1; i < n - k + 1; i++) {
        prev2 = curr2;
        curr2 = curr2->link;
    }

    if (prev1) prev1->link = curr2;
    else first = curr2;

    if (prev2) prev2->link = curr1;
    else first = curr1;

    struct Node* temp = curr1->link;
    curr1->link = curr2->link;
    curr2->link = temp;

    return first;
}

int main() {
    struct Node* first = NULL;
    int val, k;

    printf("Enter integers one by one (-1 to stop):\n");
    while (scanf("%d", &val) == 1 && val != -1) {
        first = insert(first, val);
    }

    if (!first) {
        printf("List is empty.\n");
        return 0;
    }

    printf("\nOriginal list: ");
    printList(first);

    int total = countNodes(first);
    printf("Enter K to swap Kth node from start and end (1 to %d): ", total);
    while (scanf("%d", &k) != 1 || k < 1 || k > total) {
        printf("Invalid input. Enter K between 1 and %d: ", total);
        // flush by reading invalid token
        scanf("%*s");
    }

    first = swapKth(first, k);

    printf("List after swapping %dth node from start and end: ", k);
    printList(first);

    struct Node* temp;
    while (first) {
        temp = first;
        first = first->link;
        free(temp);
    }
    return 0;
}