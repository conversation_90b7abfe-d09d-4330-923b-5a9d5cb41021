#include <stdio.h>
#include <stdlib.h>

struct Node {
    int info;
    struct Node* link;
};

// Insert at end and return updated head
struct Node* insert(struct Node* first, int value) {
    struct Node* newNode = (struct Node*)malloc(sizeof(struct Node));
    newNode->info = value;
    newNode->link = NULL;

    if (first == NULL) {
        return newNode;
    }

    struct Node* temp = first;
    while (temp->link)
        temp = temp->link;
    temp->link = newNode;
    return first;
}

// Function to print the linked list
void printList(struct Node *first) {
    struct Node *save = first;
    printf("Linked List: ");
    while (save != NULL) {
        printf("%d -> ", save->info);
        save = save->link;
    }
    printf("NULL\n");
}


// Bubble sort
void sortList(struct Node* first) {
    struct Node *i, *j;
    int temp;

    for (i = first; i != NULL; i = i->link) {
        for (j = i->link; j != NULL; j = j->link) {
            if (i->info > j->info) {
                temp = i->info;
                i->info = j->info;
                j->info = temp;
            }
        }
    }
}

int main() {
    struct Node* first = NULL;
    int val;

    printf("Enter integers one by one (-1 to stop):\n");
    while (1) {
        scanf("%d", &val);
        if (val == -1) break;
        first = insert(first, val);
    }

    printf("\nOriginal List: ");
    printList(first);

    sortList(first);

    printf("Sorted List: ");
    printList(first);

    // Free memory
    struct Node* temp;
    while (first != NULL) {
        temp = first;
        first = first->link;
        free(temp);
    }

    return 0;
}
