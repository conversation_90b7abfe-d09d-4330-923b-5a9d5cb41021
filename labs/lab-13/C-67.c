#include <stdio.h>
#include <stdlib.h>

struct Node {
    int info;
    struct Node* link;
};

// Insert at end and return new head
struct Node* insert(struct Node* first, int value) {
    struct Node* newNode = (struct Node*)malloc(sizeof(struct Node));
    newNode->info = value;
    newNode->link = NULL;

    if (first == NULL) return newNode;

    struct Node* temp = first;
    while (temp->link) temp = temp->link;
    temp->link = newNode;
    return first;
}

// Function to print the linked list
void printList(struct Node *first) {
    struct Node *save = first;
    printf("Linked List: ");
    while (save != NULL) {
        printf("%d -> ", save->info);
        save = save->link;
    }
    printf("NULL\n");
}


// Find GCD of two numbers
int findGCD(int a, int b) {
    return b == 0 ? a : findGCD(b, a % b);
}

// Insert GCD nodes between each pair of nodes
void insertGCDNodes(struct Node* first) {
    struct Node* curr = first;
    while (curr && curr->link) {
        int a = curr->info;
        int b = curr->link->info;
        int gcd = findGCD(a, b);

        struct Node* gcdNode = (struct Node*)malloc(sizeof(struct Node));
        gcdNode->info = gcd;
        gcdNode->link = curr->link;

        curr->link = gcdNode;
        curr = gcdNode->link;
    }
}

int main() {
    struct Node* first = NULL;
    int val;

    printf("Enter integers one by one (-1 to stop):\n");
    while (scanf("%d", &val) == 1 && val != -1) {
        first = insert(first, val);
    }

    if (first == NULL || first->link == NULL) {
        printf("At least two numbers are required.\n");
    } else {
        printf("\nOriginal List: ");
        printList(first);

        insertGCDNodes(first);

        printf("List after inserting GCD nodes: ");
        printList(first);
    }

    // Free the list inside main
    struct Node* temp;
    while (first) {
        temp = first;
        first = first->link;
        free(temp);
    }

    return 0;
}