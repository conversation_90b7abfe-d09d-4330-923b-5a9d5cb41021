#include <stdio.h>
#include <stdlib.h>

struct Node {
    int data;
    struct Node* next;
};

// Insert node at end and return head
struct Node* insert(struct Node* head, int value) {
    struct Node* temp = (struct Node*)malloc(sizeof(struct Node));
    temp->data = value;
    temp->next = NULL;

    if (head == NULL) {
        return temp;
    }

    struct Node* curr = head;
    while (curr->next)
        curr = curr->next;
    curr->next = temp;

    return head;
}

// Print linked list
void printList(struct Node* head) {
    while (head != NULL) {
        printf("%d ", head->data);
        head = head->next;
    }
    printf("\n");
}

// Swap pairs of nodes
struct Node* swapPairs(struct Node* head) {
    if (!head || !head->next)
        return head;

    struct Node* newHead = head->next;  // After swapping the first pair, the second node will become the new head
    struct Node* prev = NULL;            // Keeps track of the previous pair's second node to link pairs correctly
    struct Node* curr = head;            // Current node starting from the original head

    while (curr != NULL && curr->next != NULL) {         // Iterate while there are at least two nodes left to swap
        struct Node* nextPair = curr->next->next; // Store the start of the next pair (node after the next)
        struct Node* second = curr->next;          // Second node of the current pair to swap

        second->next = curr;             // Point second node to current node (swap links)
        curr->next = nextPair;           // Link current node to the next pair's first node (or NULL if none)

        if (prev)                       // If not the first pair,
            prev->next = second;         // link the previous pair's last node to the current pair's new first node

        prev = curr;                    // Update prev to current node (which is now second in the pair)
        curr = nextPair;                // Move to next pair of nodes
    }

    return newHead;                     // Return new head after swapping pairs

}

int main() {
    struct Node* head = NULL;
    int val;

    printf("Enter integers one by one (-1 to stop):\n");
    while (scanf("%d", &val) == 1 && val != -1) {
        head = insert(head, val);
    }

    printf("Original List: ");
    printList(head);

    head = swapPairs(head);

    printf("List after swapping pairs: ");
    printList(head);

    // Freeing memory in main
    struct Node* temp;
    while (head) {
        temp = head;
        head = head->next;
        free(temp);
    }

    return 0;
}