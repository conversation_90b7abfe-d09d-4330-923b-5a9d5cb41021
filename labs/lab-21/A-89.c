#include <stdio.h>
#include <stdlib.h>

int main() {
    int *arr, n, i, key, found = 0;

    // Asking the user how many elements
    printf("Enter number of elements in the array: ");
    scanf("%d", &n);

    if (n <= 0) {
        printf("Invalid array size!\n");
        return 1;
    }

    // Dynamically allocate memory
    arr = (int*)malloc(n * sizeof(int));
    if (arr == NULL) {
        printf("Memory allocation failed!\n");
        return 1;
    }

    // Taking input for the array
    printf("Enter %d elements:\n", n);
    for (i = 0; i < n; i++) {
        scanf("%d", &arr[i]);
    }

    // Asking the user for the element to search (key)
    printf("Enter the element to search: ");
    scanf("%d", &key);

    // Linear Search Logic
    for (i = 0; i < n; i++) {
        if (arr[i] == key) {
            printf("Element %d found at position %d\n", key, i ); // index position
            found = 1;
            break; // stop searching once found
        }
    }

    if (!found) {
        printf("Element %d not found in the array.\n", key);
    }

    // Free allocated memory
    free(arr);
    return 0;
}
