#include <stdio.h>

//binarySearch using Iterative method
int binarySearchIterative(int arr[], int n, int key) {
    int low = 0, high = n - 1, mid;

    while (low <= high) {
        mid = (low + high) / 2;

        if (arr[mid] == key)
            return mid; // key found
        else if (arr[mid] < key)
            low = mid + 1; // search in right half
        else
            high = mid - 1; // search in left half
    }

    return -1; // key not found
}

//binarySearch using Recursive method
int binarySearchRecursive(int arr[], int low, int high, int key) {
    if (low > high)
        return -1; // key not found

    int mid = (low + high) / 2;

    if (arr[mid] == key)
        return mid;
    else if (arr[mid] < key)
        return binarySearchRecursive(arr, mid + 1, high, key);
    else
        return binarySearchRecursive(arr, low, mid - 1, key);
}


int main() {
    int arr[100], n, i, key, result;

    printf("Enter number of elements: ");
    scanf("%d", &n);

    printf("Enter %d elements (in sorted order):\n", n);
    for (i = 0; i < n; i++) {
        scanf("%d", &arr[i]);
    }

    printf("Enter the element to search: ");
    scanf("%d", &key);

    //Iterative method
    result = binarySearchIterative(arr, n, key);
    
    //Recursive method
    result = binarySearchRecursive(arr, 0, n - 1, key);

    if (result != -1)
        printf("Element %d found at position %d\n", key, result + 1);
    else
        printf("Element %d not found.\n", key);

    return 0;
}
